# GB28181 Gateway Makefile

.PHONY: build run clean test docs swagger-install swagger-gen help

# 默认目标
all: build

# 编译项目
build:
	@echo "Building GB-Gateway..."
	go build -o gb-gateway ./cmd/gb-gateway

# 运行项目
run: build
	@echo "Starting GB-Gateway..."
	./gb-gateway

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -f gb-gateway

# 运行测试
test:
	@echo "Running tests..."
	go test ./...

# 安装 Swagger 工具
swagger-install:
	@echo "Installing Swagger tools..."
	go install github.com/swaggo/swag/cmd/swag@latest

# 生成 Swagger 文档
swagger-gen:
	@echo "Generating Swagger documentation..."
	swag init -g internal/http/server.go -o docs

# 生成文档并启动服务器
docs: swagger-gen run

# 格式化代码
fmt:
	@echo "Formatting code..."
	go fmt ./...

# 代码检查
lint:
	@echo "Running linter..."
	golangci-lint run

# 安装依赖
deps:
	@echo "Installing dependencies..."
	go mod download
	go mod tidy

# 显示帮助信息
help:
	@echo "Available targets:"
	@echo "  build         - 编译项目"
	@echo "  run           - 编译并运行项目"
	@echo "  clean         - 清理编译文件"
	@echo "  test          - 运行测试"
	@echo "  swagger-install - 安装 Swagger 工具"
	@echo "  swagger-gen   - 生成 Swagger 文档"
	@echo "  docs          - 生成文档并启动服务器"
	@echo "  fmt           - 格式化代码"
	@echo "  lint          - 代码检查"
	@echo "  deps          - 安装依赖"
	@echo "  help          - 显示此帮助信息"
