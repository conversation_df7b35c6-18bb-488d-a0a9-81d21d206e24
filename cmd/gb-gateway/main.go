package main

import (
	"flag"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"gb-gateway/internal/config"
	"gb-gateway/internal/core"
	"gb-gateway/internal/http"
	"gb-gateway/internal/sip"
	"gb-gateway/internal/state"
)

func main() {
	// 解析命令行参数
	configPath := flag.String("config", "config.yaml", "Path to configuration file")
	flag.Parse()

	// 加载配置
	cfg, err := config.Load(*configPath)
	if err != nil {
		slog.Error("Failed to load configuration", "error", err)
		os.Exit(1)
	}

	// 设置日志级别
	logLevel := cfg.GetLogLevel()
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: logLevel,
	}))
	slog.SetDefault(logger)

	slog.Info("GB-Gateway starting", "version", "1.0.0")

	// 创建状态管理器
	stateManager := state.NewInMemoryManager()

	// 创建SIP服务器
	sipServer := sip.NewServer(cfg, stateManager)

	// 创建核心业务逻辑
	coreLogic := core.NewLogic(cfg, stateManager, sipServer)

	// 创建HTTP服务器
	httpServer := http.NewServer(cfg, coreLogic)

	// 启动SIP服务器
	err = sipServer.Start()
	if err != nil {
		slog.Error("Failed to start SIP server", "error", err)
		os.Exit(1)
	}

	// 启动HTTP服务器
	err = httpServer.Start()
	if err != nil {
		slog.Error("Failed to start HTTP server", "error", err)
		sipServer.Stop()
		os.Exit(1)
	}

	slog.Info("GB-Gateway started successfully")

	// 等待信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 阻塞等待信号
	sig := <-sigChan
	slog.Info("Received signal, shutting down", "signal", sig)

	// 优雅关闭
	httpServer.Stop()
	sipServer.Stop()

	slog.Info("GB-Gateway stopped")
}
