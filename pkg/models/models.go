package models

import "time"

// Platform 代表一个注册的平台
type Platform struct {
	ID       string    `json:"id"`
	SIPURI   string    `json:"sip_uri"`
	Expires  int       `json:"expires"`
	LastSeen time.Time `json:"last_seen"`
}

// Device 代表一个摄像头设备
type Device struct {
	GBID       string `json:"gb_id"`
	Name       string `json:"name"`
	Status     string `json:"status"` // "ON", "OFF"
	IP         string `json:"ip"`
	PlatformID string `json:"platform_id"`
}

// StreamSession 代表一个点播会话
type StreamSession struct {
	SessionID   string    `json:"session_id"`
	GBID        string    `json:"gb_id"`
	SSRC        string    `json:"ssrc"`
	DialogID    string    `json:"dialog_id"`   // gosip的Dialog ID
	Destination string    `json:"destination"` // "ip:port"
	StartTime   time.Time `json:"start_time"`
}

// APIResponse 通用API响应结构
type APIResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

// StreamRequestBody 视频流请求体
type StreamRequestBody struct {
	GBID        string `json:"gb_id" binding:"required"`
	ReceiveIP   string `json:"receive_ip" binding:"required"`
	ReceivePort int    `json:"receive_port" binding:"required"`
}

// StreamResponseData 视频流响应数据
type StreamResponseData struct {
	SSRC      string `json:"ssrc"`
	SessionID string `json:"session_id"`
}

// PTZRequestBody 云台控制请求体
type PTZRequestBody struct {
	GBID    string `json:"gb_id" binding:"required"`
	Command string `json:"command" binding:"required"`
	Speed   int    `json:"speed" binding:"min=0,max=255"`
}

// PTZCommand 云台控制命令类型
type PTZCommand string

const (
	PTZCommandUp      PTZCommand = "up"
	PTZCommandDown    PTZCommand = "down"
	PTZCommandLeft    PTZCommand = "left"
	PTZCommandRight   PTZCommand = "right"
	PTZCommandZoomIn  PTZCommand = "zoom_in"
	PTZCommandZoomOut PTZCommand = "zoom_out"
	PTZCommandStop    PTZCommand = "stop"
)
